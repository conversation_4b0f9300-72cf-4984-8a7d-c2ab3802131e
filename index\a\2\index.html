<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>反差百科 - 下载页面</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
        min-height: 100vh;
        overflow-x: hidden;
        overflow-y: auto;
        position: relative;
      }

      /* 背景虚化效果 */
      .background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("assets/bg.webp");
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: blur(8px);
        transform: scale(1.1);
        z-index: -2;
      }

      .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          135deg,
          rgba(0, 0, 0, 0.4),
          rgba(0, 0, 0, 0.6)
        );
        z-index: -1;
      }

      .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 20px;
        position: relative;
        z-index: 1;
        max-width: 100%;
        box-sizing: border-box;
      }

      .app-showcase {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 60px;
        max-width: 1200px;
        width: 100%;
        margin-bottom: 40px;
      }

      .app-info {
        text-align: center;
        color: white;
        max-width: 500px;
      }

      .app-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .app-subtitle {
        font-size: 1.3rem;
        margin-bottom: 30px;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
      }

      .app-features {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        justify-content: center;
        margin-bottom: 40px;
      }

      .feature-tag {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        padding: 8px 16px;
        border-radius: 25px;
        color: white;
        font-size: 0.9rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .app-image {
        position: relative;
      }

      .app-phone {
        width: 280px;
        height: auto;
        border-radius: 25px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        transform: perspective(1000px) rotateY(-5deg);
        transition: transform 0.3s ease;
      }

      .app-phone:hover {
        transform: perspective(1000px) rotateY(0deg) scale(1.05);
      }

      .download-section {
        display: flex;
        align-items: center;
        gap: 40px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 40px;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
      }

      .qr-section {
        text-align: center;
      }

      .qr-code {
        width: 150px;
        height: 150px;
        border-radius: 15px;
        background: white;
        padding: 10px;
        margin-bottom: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .qr-title {
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .qr-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
      }

      .download-buttons {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .download-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        padding: 15px 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        min-width: 200px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
      }

      .download-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
      }

      .download-btn.secondary {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .download-btn.secondary:hover {
        background: rgba(255, 255, 255, 0.25);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
      }

      .platform-info {
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.85rem;
        margin-top: 10px;
        text-align: center;
      }

      /* 响应式设计 */
      /* 平板设备 */
      @media (max-width: 1024px) {
        .app-showcase {
          gap: 40px;
        }

        .app-title {
          font-size: 3rem;
        }

        .app-phone {
          width: 250px;
        }
      }

      /* 小屏幕设备 */
      @media (max-width: 768px) {
        body {
          overflow-y: auto;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
        }

        .background {
          filter: blur(4px);
          background-position: -580px center;
        }

        .container {
          padding: 15px;
          justify-content: flex-start;
          padding-top: 40px;
        }

        .app-showcase {
          flex-direction: column-reverse;
          gap: 30px;
          margin-bottom: 30px;
        }

        .app-title {
          font-size: 2.5rem;
          margin-bottom: 15px;
        }

        .app-subtitle {
          font-size: 1.1rem;
          margin-bottom: 20px;
        }

        .app-features {
          margin-bottom: 30px;
        }

        .feature-tag {
          font-size: 0.8rem;
          padding: 6px 12px;
        }

        .app-phone {
          width: 220px;
        }

        .download-section {
          flex-direction: column-reverse;
          gap: 25px;
          padding: 25px 20px;
          margin: 0 10px;
        }

        .qr-code {
          width: 120px;
          height: 120px;
        }

        .download-btn {
          padding: 12px 25px;
          font-size: 1rem;
          min-width: 180px;
        }
      }

      /* 手机设备 */
      @media (max-width: 480px) {
        .container {
          padding: 10px;
          padding-top: 30px;
        }

        .app-title {
          font-size: 2rem;
          margin-bottom: 12px;
        }

        .app-subtitle {
          font-size: 1rem;
          margin-bottom: 15px;
          padding: 0 10px;
        }

        .app-features {
          gap: 10px;
          margin-bottom: 25px;
        }

        .feature-tag {
          font-size: 0.75rem;
          padding: 5px 10px;
        }

        .app-phone {
          width: 180px;
        }

        .download-section {
          flex-direction: column-reverse;
          padding: 20px 15px;
          margin: 0 5px;
          gap: 20px;
        }

        .qr-code {
          width: 160px;
          height: 160px;
        }

        .qr-title {
          font-size: 1rem;
        }

        .qr-subtitle {
          font-size: 0.8rem;
        }

        .download-btn {
          padding: 10px 20px;
          font-size: 0.9rem;
          min-width: 160px;
        }

        .platform-info {
          font-size: 0.75rem;
        }
      }

      /* 小屏手机 */
      @media (max-width: 360px) {
        .app-title {
          font-size: 1.8rem;
        }

        .app-subtitle {
          font-size: 0.9rem;
        }

        .app-phone {
          width: 160px;
        }

        .download-section {
          flex-direction: column-reverse;
          padding: 15px 10px;
        }

        .qr-code {
          width: 90px;
          height: 90px;
        }

        .download-btn {
          min-width: 140px;
          font-size: 0.85rem;
        }
      }

      /* 动画效果 */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .container > * {
        animation: fadeInUp 0.8s ease-out;
      }

      .app-showcase {
        animation-delay: 0.2s;
      }

      .download-section {
        animation-delay: 0.4s;
      }

      /* 隐藏类 */
      .hidden {
        display: none !important;
      }

      /* 气泡提醒动画 */
      .bubble-notification {
        position: fixed;
        top: -100px;
        left: 50%;
        transform: translateX(-50%);
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.95),
          rgba(255, 255, 255, 0.85)
        );
        backdrop-filter: blur(15px);
        color: #333;
        padding: 12px 20px;
        border-radius: 25px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        font-size: 0.9rem;
        font-weight: 500;
        z-index: 1000;
        animation: bubbleSlideDown 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)
            forwards,
          bubbleFadeOut 0.6s ease-out 4s forwards;
        max-width: 90%;
        text-align: center;
        line-height: 1.4;
      }

      @keyframes bubbleSlideDown {
        0% {
          top: -100px;
          opacity: 0;
          transform: translateX(-50%) scale(0.8);
        }
        100% {
          top: 20px;
          opacity: 1;
          transform: translateX(-50%) scale(1);
        }
      }

      @keyframes bubbleFadeOut {
        0% {
          opacity: 1;
          transform: translateX(-50%) scale(1);
        }
        100% {
          opacity: 0;
          transform: translateX(-50%) scale(0.95);
        }
      }

      /* 移动端气泡优化 */
      @media (max-width: 768px) {
        .bubble-notification {
          font-size: 0.85rem;
          padding: 10px 16px;
          width: 15rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="background"></div>
    <div class="overlay"></div>

    <!-- 气泡提醒 -->
    <div class="bubble-notification" id="bubbleNotification">
      😊 哎呀，一不小心进入广告了~
    </div>

    <div class="container">
      <div class="app-showcase">
        <div class="app-info">
          <h1 class="app-title">反差百科</h1>
          <p class="app-subtitle">记录同一个她，室内室外的变化</p>
          <p class="app-subtitle">海量视频 免费观看</p>
        </div>
        <div class="app-image">
          <img
            src="https://i.ibb.co/fVGvG6FM/image.png"
            alt="反差百科"
            class="app-phone"
          />
        </div>
      </div>

      <div class="download-section">
        <div class="qr-section">
          <img src="assets/qrcode.png" alt="下载二维码" class="qr-code" />
          <div class="qr-title">手机浏览器扫码下载</div>
          <div class="qr-subtitle">支持 Android</div>
        </div>

        <div class="download-buttons">
          <button id="downloadBtn" class="download-btn">
            <span>📱</span>
            <span>立即下载</span>
          </button>
          <div class="platform-info" id="platformInfo">
            检测到您的设备平台...
          </div>
        </div>
      </div>
    </div>

    <script
      defer
      src="https://statistics.aini.one/script.js"
      data-website-id="1067bedf-7f7f-44a3-8151-dd004ef850c4"
    ></script>
    <script>
      // 平台检测
      function detectPlatform() {
        const platform = navigator.platform.toLowerCase();
        const userAgent = navigator.userAgent.toLowerCase();

        // 使用navigator.platform进行主要判断
        const isWindows = platform.includes("win");
        const isMac = platform.includes("mac");
        const isLinux = platform.includes("linux");

        // 移动设备检测（结合platform和userAgent）
        const isIOS =
          platform.includes("iphone") ||
          platform.includes("ipad") ||
          platform.includes("ipod") ||
          /iphone|ipad|ipod/i.test(userAgent);
        const isAndroid =
          platform.includes("android") || /android/i.test(userAgent);

        // 判断是否为移动设备
        const isMobile = isIOS || isAndroid || /mobile|tablet/i.test(userAgent);

        // PC设备判断
        const isPC = (isWindows || isMac || isLinux) && !isMobile;

        return {
          isMobile,
          isIOS,
          isAndroid,
          isWindows,
          isMac,
          isLinux,
          isPC,
          platform: navigator.platform,
        };
      }

      // 初始化页面
      function initPage() {
        const platform = detectPlatform();
        const downloadBtn = document.getElementById("downloadBtn");
        const platformInfo = document.getElementById("platformInfo");
        const webBtn = document.getElementById("webBtn");

        // 所有平台都显示下载按钮
        downloadBtn.style.display = "flex";

        // 根据平台显示不同信息
        if (platform.isWindows) {
          platformInfo.textContent = "检测到Windows系统 - 点击下载apk";
          downloadBtn.innerHTML = "<span>💻</span><span>立即下载</span>";
        } else if (platform.isMac) {
          platformInfo.textContent = "检测到Mac系统 - 点击下载apk";
          downloadBtn.innerHTML = "<span>🍎</span><span>立即下载</span>";
        } else if (platform.isIOS) {
          platformInfo.textContent = "检测到iOS设备 - 点击下载或扫码";
          downloadBtn.innerHTML = "<span>📱</span><span>立即下载</span>";
        } else if (platform.isAndroid) {
          platformInfo.textContent = "检测到Android设备 - 点击下载或扫码";
          downloadBtn.innerHTML = "<span>🤖</span><span>立即下载</span>";
        } else {
          platformInfo.textContent = "点击下载或扫码获取应用";
          downloadBtn.innerHTML = "<span>📱</span><span>立即下载</span>";
        }

        downloadBtn.addEventListener("click", function () {
          window.location.href =
            "https://fc56e3ce0.r5y6w5uk.com/?code=hnZk1Mxd";
        });
      }

      document.addEventListener("DOMContentLoaded", initPage);

      document.addEventListener("mousemove", function (e) {
        const cards = document.querySelectorAll(
          ".download-section, .app-phone"
        );
        cards.forEach((card) => {
          const rect = card.getBoundingClientRect();
          const x = e.clientX - rect.left;
          const y = e.clientY - rect.top;

          if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;

            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
          }
        });
      });

      document.addEventListener("mouseleave", function () {
        const cards = document.querySelectorAll(".download-section");
        cards.forEach((card) => {
          card.style.transform =
            "perspective(1000px) rotateX(0deg) rotateY(0deg)";
        });
      });
    </script>
  </body>
</html>
