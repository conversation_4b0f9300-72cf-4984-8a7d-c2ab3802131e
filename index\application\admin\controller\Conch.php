<?php
if(!function_exists('sg_load')){$__v=phpversion();$__x=explode('.',$__v);$__v2=$__x[0].'.'.(int)$__x[1];$__u=strtolower(substr(php_uname(),0,3));$__ts=(@constant('PHP_ZTS') || @constant('ZEND_THREAD_SAFE')?'ts':'');$__f=$__f0='ixed.'.$__v2.$__ts.'.'.$__u;$__ff=$__ff0='ixed.'.$__v2.'.'.(int)$__x[2].$__ts.'.'.$__u;$__ed=@ini_get('extension_dir');$__e=$__e0=@realpath($__ed);$__dl=function_exists('dl') && function_exists('file_exists') && @ini_get('enable_dl') && !@ini_get('safe_mode');if($__dl && $__e && version_compare($__v,'5.2.5','<') && function_exists('getcwd') && function_exists('dirname')){$__d=$__d0=getcwd();if(@$__d[1]==':') {$__d=str_replace('\\','/',substr($__d,2));$__e=str_replace('\\','/',substr($__e,2));}$__e.=($__h=str_repeat('/..',substr_count($__e,'/')));$__f='/ixed/'.$__f0;$__ff='/ixed/'.$__ff0;while(!file_exists($__e.$__d.$__ff) && !file_exists($__e.$__d.$__f) && strlen($__d)>1){$__d=dirname($__d);}if(file_exists($__e.$__d.$__ff)) dl($__h.$__d.$__ff); else if(file_exists($__e.$__d.$__f)) dl($__h.$__d.$__f);}if(!function_exists('sg_load') && $__dl && $__e0){if(file_exists($__e0.'/'.$__ff0)) dl($__ff0); else if(file_exists($__e0.'/'.$__f0)) dl($__f0);}if(!function_exists('sg_load')){$__ixedurl='http://www.sourceguardian.com/loaders/download.php?php_v='.urlencode($__v).'&php_ts='.($__ts?'1':'0').'&php_is='.@constant('PHP_INT_SIZE').'&os_s='.urlencode(php_uname('s')).'&os_r='.urlencode(php_uname('r')).'&os_m='.urlencode(php_uname('m'));$__sapi=php_sapi_name();if(!$__e0) $__e0=$__ed;if(function_exists('php_ini_loaded_file')) $__ini=php_ini_loaded_file(); else $__ini='php.ini';if((substr($__sapi,0,3)=='cgi')||($__sapi=='cli')||($__sapi=='embed')){$__msg="\nPHP script '".__FILE__."' is protected by SourceGuardian and requires a SourceGuardian loader '".$__f0."' to be installed.\n\n1) Download the required loader '".$__f0."' from the SourceGuardian site: ".$__ixedurl."\n2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="\n3) Edit ".$__ini." and add 'extension=".$__f0."' directive";}}$__msg.="\n\n";}else{$__msg="<html><body>PHP script '".__FILE__."' is protected by <a href=\"http://www.sourceguardian.com/\">SourceGuardian</a> and requires a SourceGuardian loader '".$__f0."' to be installed.<br><br>1) <a href=\"".$__ixedurl."\" target=\"_blank\">Click here</a> to download the required '".$__f0."' loader from the SourceGuardian site<br>2) Install the loader to ";if(isset($__d0)){$__msg.=$__d0.DIRECTORY_SEPARATOR.'ixed';}else{$__msg.=$__e0;if(!$__dl){$__msg.="<br>3) Edit ".$__ini." and add 'extension=".$__f0."' directive<br>4) Restart the web server";}}$__msg.="</body></html>";}die($__msg);exit();}}return sg_load('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');
