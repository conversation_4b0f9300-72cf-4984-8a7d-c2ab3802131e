<?php
return array (
  '__pattern__' => 
  array (
    'id' => '[\\s\\S]*?',
    'ids' => '[\\s\\S]*?',
    'wd' => '[\\s\\S]*',
    'en' => '[\\s\\S]*?',
    'state' => '[\\s\\S]*?',
    'area' => '[\\s\\S]*',
    'year' => '[\\s\\S]*?',
    'lang' => '[\\s\\S]*?',
    'letter' => '[\\s\\S]*?',
    'actor' => '[\\s\\S]*?',
    'director' => '[\\s\\S]*?',
    'tag' => '[\\s\\S]*?',
    'class' => '[\\s\\S]*?',
    'order' => '[\\s\\S]*?',
    'by' => '[\\s\\S]*?',
    'file' => '[\\s\\S]*?',
    'name' => '[\\s\\S]*?',
    'url' => '[\\s\\S]*?',
    'type' => '[\\s\\S]*?',
    'sex' => '[\\s\\S]*?',
    'version' => '[\\s\\S]*?',
    'blood' => '[\\s\\S]*?',
    'starsign' => '[\\s\\S]*?',
    'page' => '\\d+',
    'ajax' => '\\d+',
    'tid' => '\\d+',
    'mid' => '\\d+',
    'rid' => '\\d+',
    'pid' => '\\d+',
    'sid' => '\\d+',
    'nid' => '\\d+',
    'uid' => '\\d+',
    'level' => '\\d+',
    'score' => '\\d+',
    'limit' => '\\d+',
  ),
  'map' => 
  array (
    0 => 'map/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/index' => 
  array (
    0 => 'rss/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/baidu' => 
  array (
    0 => 'rss/baidu',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/google' => 
  array (
    0 => 'rss/google',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/sogou' => 
  array (
    0 => 'rss/sogou',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/so' => 
  array (
    0 => 'rss/so',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/bing' => 
  array (
    0 => 'rss/bing',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'rss/sm' => 
  array (
    0 => 'rss/sm',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'index-<page?>' => 
  array (
    0 => 'index/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'gbook-<page?>' => 
  array (
    0 => 'gbook/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'gbook$' => 
  array (
    0 => 'gbook/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'topic-<page?>' => 
  array (
    0 => 'topic/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'topic$' => 
  array (
    0 => 'topic/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'topicdetail-<id>' => 
  array (
    0 => 'topic/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'actor-<page?>' => 
  array (
    0 => 'actor/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'actor$' => 
  array (
    0 => 'actor/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'actordetail-<id>' => 
  array (
    0 => 'actor/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'actorshow/<area?>-<blood?>-<by?>-<letter?>-<level?>-<order?>-<page?>-<sex?>-<starsign?>' => 
  array (
    0 => 'actor/show',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'role-<page?>' => 
  array (
    0 => 'role/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'role$' => 
  array (
    0 => 'role/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'roledetail-<id>' => 
  array (
    0 => 'role/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'roleshow/<by?>-<letter?>-<level?>-<order?>-<page?>-<rid?>' => 
  array (
    0 => 'role/show',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodtype/<id>-<page?>' => 
  array (
    0 => 'vod/type',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodtype/<id>' => 
  array (
    0 => 'vod/type',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'voddetail/<id>' => 
  array (
    0 => 'vod/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodrss-<id>' => 
  array (
    0 => 'vod/rss',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodplay/<id>-<sid>-<nid>' => 
  array (
    0 => 'vod/play',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'voddown/<id>-<sid>-<nid>' => 
  array (
    0 => 'vod/down',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodshow/<id>-<area?>-<by?>-<class?>-<lang?>-<letter?>-<level?>-<order?>-<page?>-<state?>-<tag?>-<year?>' => 
  array (
    0 => 'vod/show',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodsearch/<wd?>-<actor?>-<area?>-<by?>-<class?>-<director?>-<lang?>-<letter?>-<level?>-<order?>-<page?>-<state?>-<tag?>-<year?>' => 
  array (
    0 => 'vod/search',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodplot/<id>-<page?>' => 
  array (
    0 => 'vod/plot',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'vodplot/<id>' => 
  array (
    0 => 'vod/plot',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'arttype/<id>-<page?>' => 
  array (
    0 => 'art/type',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'arttype/<id>' => 
  array (
    0 => 'art/type',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'artshow-<id>' => 
  array (
    0 => 'art/show',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'artdetail-<id>-<page?>' => 
  array (
    0 => 'art/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'artdetail-<id>' => 
  array (
    0 => 'art/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'artrss-<id>-<page>' => 
  array (
    0 => 'art/rss',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'artshow/<id>-<by?>-<class?>-<level?>-<letter?>-<order?>-<page?>-<tag?>' => 
  array (
    0 => 'art/show',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'artsearch/<wd?>-<by?>-<class?>-<level?>-<letter?>-<order?>-<page?>-<tag?>' => 
  array (
    0 => 'art/search',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'label-<file>' => 
  array (
    0 => 'label/index',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'plotdetail/<id>-<page?>' => 
  array (
    0 => 'plot/plot',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
  'plotdetail/<id>' => 
  array (
    0 => 'plot/detail',
    1 => 
    array (
    ),
    2 => 
    array (
    ),
  ),
);