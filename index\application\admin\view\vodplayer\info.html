{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <form class="layui-form layui-form-pane" method="post" action="">
        <input type="hidden" name="__token__" value="{$Request.token}" />
        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">基本设置</li>
                <li>播放器代码</li>
            </ul>
            <div class="layui-tab-content">

                <div class="layui-tab-item layui-show">

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('status')}：</label>
                        <div class="layui-input-block">
                            <input name="status" type="radio" id="rad-1" value="0" title="{:lang('disable')}" {if condition="$info['status'] neq 1"}checked {/if}>
                            <input name="status" type="radio" id="rad-2" value="1" title="{:lang('enable')}" {if condition="$info['status'] eq 1"}checked {/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('code')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="{$info.from}" placeholder="{:lang('admin/vodplayer/code_tip')}" id="from" name="from" {if condition="$info.from neq ''"} readonly="readonly"{/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('name')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="{$info.show}" placeholder="" id="show" name="show">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('remarks')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="{$info.des}" placeholder="" id="des" name="des">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('target')}：</label>
                        <div class="layui-input-block">
                            <input name="target" type="radio" value="_self" title="{:lang('current')}" {if condition="$info['target'] neq '_blank'"}checked {/if}>
                            <input name="target" type="radio" value="_blank" title="{:lang('blank')}" {if condition="$info['target'] eq '_blank'"}checked {/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('status_parse')}：</label>
                        <div class="layui-input-block">
                            <input name="ps" type="radio" value="0" title="{:lang('disable')}" {if condition="$info['ps'] neq 1"}checked {/if}>
                            <input name="ps" type="radio" value="1" title="{:lang('enable')}" {if condition="$info['ps'] eq 1"}checked {/if}>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/vodplayer/api_url')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="{$info.parse}" placeholder="{:lang('admin/vodplayer/api_url_tip')}" id="parse" name="parse">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('sort')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="{$info.sort}" placeholder="{:lang('admin/vodplayer/sort_tip')}" id="sort" name="sort">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('tip')}：</label>
                        <div class="layui-input-block">
                            <textarea name="tip" cols="" style="height:50px;min-height:50px;" class="layui-textarea"  placeholder="" >{$info.tip}</textarea>
                        </div>
                    </div>
              </div>
                <div class="layui-tab-item">
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('file')}：</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="/static/player/{$info.from}.js" disabled="disabled">

                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('code')}：</label>
                        <div class="layui-input-block">
                            <textarea name="code" cols="" rows="20" class="layui-textarea"  placeholder="" >{$info.code}</textarea>
                        </div>
                    </div>
                </div>

        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="true">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>

</div>
{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript">

    layui.use(['form', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery;

        // 验证
        form.verify({
            from: function (value) {
                if (value == "") {
                    return "{:lang('admin/vodplayer/code_empty')}";
                }
            },
            show: function (value) {
                if (value == "") {
                    return "{:lang('name_empty')}";
                }
            }
        });


    });
</script>

</body>
</html>