<?php
return array (
  'theme' => 
  array (
    'logo' => 
    array (
      'bpic' => 'template/conch/asset/img/logo_black.png',
      'wpic' => 'template/conch/asset/img/logo_white.png',
      'icon' => 'template/conch/asset/img/favicon.png',
      'webapp' => 'template/conch/asset/img/ios_fav.png',
    ),
    'lazy' => 'template/conch/asset/img/load.gif',
    'tj' => 
    array (
      'btn' => '1',
    ),
    'head' => 
    array (
      'text' => '',
    ),
    'foot' => 
    array (
      'text' => '<p>本站只提供WEB页面服务，本站不存储、不制作任何视频，不承担任何由于内容的合法性及健康性所引起的争议和法律责任。</p>
<p>若本站收录内容侵犯了您的权益，请附说明联系邮箱，本站将第一时间处理。</p>',
    ),
    'type' => 
    array (
      'hom' => '1,2,3,4',
      'meunbtn' => '1',
      'meunid' => '1,2,3,4',
      'meunys' => 'one',
      'ht' => '1,2',
      'ho' => '3,4',
      'hb' => '999',
      'zb' => '999',
      'mx' => '999',
    ),
    'vod' => 
    array (
      'hnum' => '12',
      'num' => '6',
    ),
    'notice' => 
    array (
      'btn' => '1',
      'text' => '<span style="font-size:0.5rem;">欢迎来到爱你影视，资源永久免费 - <span style="color:orange;">1秒记住网址：tv.aini.one</span>；<span style="color:#ffee00;">关于广告跳转，请多尝试返回重新进入网站会好很多，感谢支持！</span><span style="color:orange;">备用域名：ainitv.icu</span></span>',
    ),
    'hotvod' => 
    array (
      'btn' => '1',
      'title' => 'Ani ohev otach',
      'num' => '12',
    ),
    'art' => 
    array (
      'hbtn' => '1',
      'htitle' => '热点资讯',
      'hnum' => '6',
    ),
    'topic' => 
    array (
      'hbtn' => '1',
      'hnum' => '6',
      'title' => '专题',
      'btn' => '1',
    ),
    'actor' => 
    array (
      'hbtn' => '1',
      'htitle' => '荧幕热星',
      'title' => '明星',
      'btn' => '1',
    ),
    'rank' => 
    array (
      'hbtn' => '1',
      'hby' => 'week',
      'hid' => '1,2,3,4',
      'vby' => 'week',
      'btn' => '1',
      'title' => '排行榜',
      'num' => '6',
      'id' => '1,2,3,4,6,7',
    ),
    'links' => 
    array (
      'btn' => '1',
      'title' => '友情链接',
      'num' => '30',
    ),
    'banner' => 
    array (
      'btn' => '1',
      'ms' => 'small',
      'smbg' => '1',
      'bgstyle' => '',
    ),
    'lbbanner' => 
    array (
      'btn' => '1',
    ),
    'search' => 
    array (
      'text' => '海量影片精彩看不停',
      'lxbtn' => '1',
    ),
    'play' => 
    array (
      'height' => '56.25',
      'adbtn' => '1',
      'nbtn' => '1',
      'notice' => '<li><span class=\'mytip\'>提醒</span>主域名已被墙！请收藏 tv.aini.one，tv2.aini.one，tv3.aini.one</li>
      <li><span class=\'mytip\'>提醒</span>不要轻易相信视频中的广告，谨防上当受骗!</li>
<li>一秒记住域名：<span style="color:orange;">tv.aini.one</span>（爱你一个）</li>
<li>全屏播放可避免跳出广告弹窗</li>
<li><span style="color:orange;font-size:0.4rem">备用域名：ainitv.icu</span></li>
',
    ),
    'show' => 
    array (
      'filter' => 'a|b|c|d|e',
    ),
    'playlink' => 
    array (
      'btn' => '0',
    ),
    'nav' => 
    array (
      'id' => '1,2,3,4,5,22',
      'zdybtn' => '0',
      'zdybtn1' => '0',
      'zdyname1' => '音乐MV',
      'zdylink1' => '/',
      'zdypic1' => 'template/conch/asset/img/ios_fav.png',
      'zdybtn2' => '0',
      'zdyname2' => '',
      'zdylink2' => '',
      'zdypic2' => '',
      'zdybtn3' => '0',
      'zdyname3' => '',
      'zdylink3' => '',
      'zdypic3' => '',
      'zdybtn4' => '0',
      'zdyname4' => '',
      'zdylink4' => '',
      'zdypic4' => '',
    ),
    'rtnav' => 
    array (
      'ym' => 'a|c|e|h|i',
      'zdybtn1' => '0',
      'zdyname1' => '',
      'zdylink1' => '',
      'zdybtn2' => '0',
      'zdyname2' => '',
      'zdylink2' => '',
    ),
    'fnav' => 
    array (
      'btn' => '1',
      'id' => '1,2,3,4,22,5',
      'ym' => 'h',
      'zdybtn1' => '0',
      'zdyname1' => '连续剧',
      'zdyicon1' => '&#xe649;',
      'zdylink1' => '/index.php/vod/type/id/2.html',
      'zdybtn2' => '0',
      'zdyname2' => '综艺',
      'zdyicon2' => '&#xe64b;',
      'zdylink2' => '/index.php/vod/type/id/3.html',
      'zdybtn3' => '0',
      'zdyname3' => '动漫',
      'zdyicon3' => '&#xe648;',
      'zdylink3' => '/index.php/vod/type/id/4.html',
      'zdybtn4' => '0',
      'zdyname4' => '短剧',
      'zdyicon4' => '&#xe647;',
      'zdylink4' => '/index.php/vod/type/id/22.html',
      'zdybtn5' => '0',
      'zdyname5' => '资讯',
      'zdyicon5' => '&#xe630;',
      'zdylink5' => '/index.php/art/type/id/5.html',
    ),
    'share' => 
    array (
      'bdbtn' => '0',
      'api' => 'bd',
      'link' => '',
      'apiurl' => '',
      'tok' => '',
      'term' => 'long',
    ),
    'qq' => 
    array (
      'btn' => '1',
      'title' => '暂无',
      'link' => '#'//'https://t.me/+FvheGrPmtwRiMzhh',
    ),
    'weixin' => 
    array (
      'btn' => '1',
      'btntext' => '请在手机/电脑浏览器访问',
      'qrcode' => 'template/conch/asset/img/tv_aini_tg.jpg',
      'title' => '请在手机/电脑浏览器访问',
      'text' => ''//'<p>您还可加入 <a href="https://t.me/+FvheGrPmtwRiMzhh" target="_black">TG 交流</a></p>',#'<p>长按识别二维码或微信扫码关注</p><p>关注后回复片名即可</p><p>或微信搜索微信名：<span class=\'mycol\'>躺平的卷毛</span></p>',
    ),
    'zans' => 
    array (
      'btn' => '1',
      'qrcode' => 'template/conch/asset/img/tv_aini_tg.jpg',
      'title' => '打开TG识别二维码',
      'text' => ''//'<p>欢迎来到 <a href="https://t.me/+FvheGrPmtwRiMzhh" target="_black">TG</a> 交流</p>',
    ),
    'apps' => 
    array (
      'btn' => '1',
      'link' => '',
    ),
    'map' => 
    array (
      'btn' => '1',
      'title' => '最近更新',
      'id' => '',
    ),
    'color' => 
    array (
      'select' => '0',
      'sbtn' => '1',
      'ms' => 'black',
      'mbtn' => '1',
    ),
    'role' => 
    array (
      'title' => '角色',
      'btn' => '1',
    ),
    'plot' => 
    array (
      'title' => '剧情',
      'btn' => '1',
    ),
    'gbook' => 
    array (
      'title' => '留言',
    ),
    'user' => 
    array (
      'title' => '会员',
    ),
    'font' => '0',
    'seos' => 
    array (
      'index_name' => '',
      'index_key' => '',
      'index_des' => '',
      'detail_name' => '',
      'detail_key' => '',
      'detail_des' => '',
      'play_name' => '',
      'play_key' => '',
      'play_des' => '',
      'down_name' => '',
      'down_key' => '',
      'down_des' => '',
      'arti_name' => '',
      'arti_key' => '',
      'arti_des' => '',
      'artd_name' => '',
      'artd_key' => '',
      'artd_des' => '',
      'topic_name' => '',
      'topic_key' => '',
      'topic_des' => '',
      'topicd_name' => '',
      'topicd_key' => '',
      'topicd_des' => '',
    ),
    'ads' => 
    array (
      'btn' => '1',
      'bottom' => 
      array (
        'btn' => '0',
        'content' => '<a href="#"><img src="/template/conch/txyunad/newuser/1040x100.jpg" /></a>',
      ),
      'all' => 
      array (
        'btn' => '1',
        'content' => '',#'<a href="https://url.cn/IHP0w2xM" target="_black"><img src="/template/conch/txyunad/tehui/1200X90.jpg" /></a>', #顶部banner1200X90（包含首页、列表、详情、播放页）
      ),
      'banner' => 
      array (
        'btn' => '0',
        'tbtn' => '0',
        'title' => '',
        'sub' => '',
        'link' => '',
        'pic' => '',
      ),
      'vod_w' => 
      array (
        'btn' => '1',
        'content' => '',#'<a href="https://url.cn/7q98hZVg" target="_black"><img src="/template/conch/txyunad/newuser/1040x100.jpg" /></a>', #详情页和播放页下面长条广告位置1040x100
      ),
      'vod_r' => 
      array (
        'btn' => '1',
        'content' => '',#'<a href="https://url.cn/7q98hZVg" target="_black"><img src="/template/conch/txyunad/newuser/500x500.jpg" /></a>', # 详情页和播放页右侧大正方形广告位 500x500
      ),
      'play' => 
      array (
        'btn' => '1',
        'tbtn' => '1',
        'title' => '反差百科',
        'link' => '/a/2/index.html',
        'pic' => 'https://i.ibb.co/fVGvG6FM/image.png', # 播放器页猜你喜欢第一个广告位置
      ),
      'search_v' => 
      array (
        'btn' => '1',
        'tbtn' => '1',
        'title' => '反差百科',
        'sub' => 'aini.one',  
        'link' => '/a/2/index.html',
        'pic' => 'https://i.ibb.co/fVGvG6FM/image.png', # 搜索页第一个广告位
      ),
      'art_w' => 
      array (
        'btn' => '1',
        'content' => '<a href="#"><img src="http://yanshi.sdpiyou.com/ads-example.jpg" /></a>',
      ),
      'art_r' => 
      array (
        'btn' => '1',
        'content' => '<a href="#"><img src="http://yanshi.sdpiyou.com/ads-example.jpg" /></a>',
      ),
      'artlist' => 
      array (
        'btn' => '1',
        'tbtn' => '1',
        'title' => '测试广告',
        'link' => '',
        'pic' => 'http://yanshi.sdpiyou.com/ads-example.jpg',
      ),
      'user' => 
      array (
        'btn' => '1',
        'pic' => 'http://yanshi.sdpiyou.com/ads-example.jpg',
      ),
    ),
  ),
);