{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <div class="my-toolbar-box">

        <div class="layui-btn-group">
            <a data-href="{:url('info')}" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>{:lang('add')}</a>
            <a data-href="{:url('index/select')}?tab=vod&col=status&tpl=select_state&url=voddowner/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('status')}</a>
            <a data-href="{:url('index/select')}?tab=vod&col=ps&tpl=select_state&url=voddowner/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('status_parse')}</a>
        </div>

    </div>

    <form class="layui-form " method="post" id="pageListForm">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="40">{:lang('sort')}</th>
                <th width="40">{:lang('code')}</th>
                <th width="130">{:lang('name')}</th>
                <th width="50">{:lang('status')}</th>
                <th width="50">{:lang('status_parse')}</th>
                <th width="50">{:lang('target')}</th>
                <th width="130">{:lang('admin/vodplayer/alone_api_url')}</th>
                <th width="130">{:lang('remarks')}</th>
                <th>{:lang('tip')}</th>
                <th width="130">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.from}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.sort}</td>
                <td>{$vo.from}</td>
                <td>{$vo.show}</td>
                <td>{if condition="$vo.status eq 1"}<span class="layui-badge layui-bg-green">{:lang('enable')}</span>{else}<span class="layui-badge">{:lang('disable')}</span>{/if} </td>
                <td>{if condition="$vo.ps eq 1"}<span class="layui-badge layui-bg-green">{:lang('enable')}</span>{else}<span class="layui-badge">{:lang('disable')}</span>{/if} </td>
                <td>{if condition="$vo.target neq '_blank'"}<span class="layui-badge layui-bg-green">{:lang('current')}</span>{else}<span class="layui-badge">{:lang('blank')}</span>{/if} </td>
                <td>{$vo.parse}</td>
                <td>{$vo.des}</td>
                <td>{$vo.tip}</td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-href="{:url('info?id='.$vo['from'])}" href="javascript:;" title="{:lang('edit')}">{:lang('edit')}</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['from'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

    </form>
</div>
{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">

</script>
</body>
</html>