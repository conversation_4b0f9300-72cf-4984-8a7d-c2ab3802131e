{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box" >
        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('index')}">
                <div class="layui-input-inline w150">
                    <select name="status">
                        <option value="">{:lang('select_status')}</option>
                        <option value="0" {if condition="$param['status'] eq '0'"}selected {/if}>{:lang('reviewed_not')}</option>
                        <option value="1" {if condition="$param['status'] eq '1'"}selected {/if}>{:lang('reviewed')}</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('del')}?ids=1&all=1" class="layui-btn layui-btn-primary j-ajax" confirm="{:lang('clear_confirm')}"><i class="layui-icon">&#xe640;</i>{:lang('clear')}</a>
            <a data-href="{:url('audit')}" class="layui-btn layui-btn-primary j-page-btns confirm" confirm="{:lang('audit_confirm')}"><i class="layui-icon">&#xe640;</i>{:lang('audit')}</a>
        </div>
    </div>

     <form class="layui-form " method="post" id="pageListForm">
         <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="50">{:lang('id')}</th>
                <th width="50">{:lang('user')}</th>
                <th width="50">{:lang('status')}</th>
                <th width="50">{:lang('points')}</th>
                <th width="50">{:lang('money')}</th>
                <th width="50">{:lang('bank')}</th>
                <th width="50">{:lang('account')}</th>
                <th width="50">{:lang('name')}</th>
                <th width="100">{:lang('remarks')}</th>
                <th width="100">{:lang('time')}</th>
                <th width="100">{:lang('audit_time')}</th>
                <th width="50">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.cash_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.cash_id}</td>
                <td>[{$vo.user_id}]{$vo.user_name|htmlspecialchars}</td>
                <td>{if condition="$vo.cash_status eq 1"}<span class="layui-badge layui-bg-green">{:lang('reviewed')}</span>{else/}<span class="layui-badge">{:lang('reviewed_not')}</span>{/if}</td>
                <td>{$vo.cash_points}</td>
                <td>{$vo.cash_money}</td>
                <td>{$vo.cash_bank_name|htmlspecialchars}</td>
                <td>{$vo.cash_bank_no|htmlspecialchars}</td>
                <td>{$vo.cash_payee_name|htmlspecialchars}</td>
                <td>{$vo.cash_remarks|htmlspecialchars}</td>
                <td>{$vo.cash_time|mac_day=color}</td>
                <td>{$vo.cash_time_audit|mac_day=color}</td>
                <td>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['cash_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                    {if condition="$vo.cash_status neq '1'"}
                    <a class="layui-badge-rim j-ajax" confirm="{:lang('audit_confirm')}" data-href="{:url('audit?ids='.$vo['cash_id'])}" href="javascript:;" title="{:lang('audit')}">{:lang('audit')}</a>
                    {/if}
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

        <div id="pages" class="center"></div>

    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}


<script type="text/javascript">
    var curUrl="{:url('cash/index',$param)}";
    layui.use(['laypage', 'layer'], function() {
        var laypage = layui.laypage
                , layer = layui.layer;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });
    });
</script>
</body>
</html>