{include file="../../../application/admin/view/public/head" /}

<div class="page-container">
    <form class="layui-form layui-form-pane" action="">
        <input type="hidden" name="__token__" value="{$Request.token}" />
        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">{:lang('admin/system/configplay/title')}</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">

                    <blockquote class="layui-elem-quote layui-quote-nm">
                        {:lang('admin/system/configplay/tip')}
                    </blockquote>

                <div class="layui-form-item" >
                    <label class="layui-form-label">{:lang('admin/system/configplay/width')}：</label>
                    <div class="layui-input-inline w150">
                        <input type="text" name="play[width]" placeholder="{:lang('admin/system/configplay/width_tip')}" value="{$play.width}" class="layui-input ">
                    </div>
                </div>
                <div class="layui-form-item" >
                    <label class="layui-form-label">{:lang('admin/system/configplay/height')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[height]" placeholder="{:lang('admin/system/configplay/height_tip')}" value="{$play.height}" class="layui-input w150">
                    </div>
                </div>
                <div class="layui-form-item" >
                    <label class="layui-form-label">{:lang('admin/system/configplay/widthmob')}：</label>
                    <div class="layui-input-inline w150">
                        <input type="text" name="play[widthmob]" placeholder="{:lang('admin/system/configplay/width_tip')}" value="{$play.widthmob}" class="layui-input ">
                    </div>
                </div>
                <div class="layui-form-item" >
                    <label class="layui-form-label">{:lang('admin/system/configplay/heightmob')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[heightmob]" placeholder="{:lang('admin/system/configplay/height_tip')}" value="{$play.heightmob}" class="layui-input w150">
                    </div>
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label">{:lang('admin/system/configplay/widthpop')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[widthpop]" placeholder="{:lang('admin/system/configplay/width_tip')}" value="{$play.widthpop}" class="layui-input w150">
                    </div>
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label">{:lang('admin/system/configplay/heightpop')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[heightpop]" placeholder="{:lang('admin/system/configplay/height_tip')}" value="{$play.heightpop}" class="layui-input w150">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('admin/system/configplay/second')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[second]" placeholder="{:lang('admin/system/configplay/second_tip')}" value="{$play.second}" class="layui-input w150">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('admin/system/configplay/prestrain')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[prestrain]" placeholder="{:lang('admin/system/configplay/prestrain_tip')}" value="{$play.prestrain}" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('admin/system/configplay/buffer')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="play[buffer]" placeholder="{:lang('admin/system/configplay/buffer_tip')}" value="{$play.buffer}" class="layui-input">
                    </div>
                </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configplay/parse')}：</label>
                        <div class="layui-input-block">
                            <input type="text" name="play[parse]" placeholder="{:lang('admin/system/configplay/parse_tip')}" value="{$play.parse}" class="layui-input">
                        </div>
                    </div>


                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('admin/system/configplay/autofull')}：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="play[autofull]" value="0" title="{:lang('close')}" {if condition="$play.autofull neq 1"}checked {/if}>
                        <input type="radio" name="play[autofull]" value="1" title="{:lang('open')}" {if condition="$play.autofull eq 1"}checked {/if}>
                    </div>
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label">{:lang('admin/system/configplay/showtop')}：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="play[showtop]" value="0" title="{:lang('close')}" {if condition="$play.showtop neq 1"}checked {/if}>
                        <input type="radio" name="play[showtop]" value="1" title="{:lang('open')}" {if condition="$play.showtop eq 1"}checked {/if}>
                    </div>
                </div>
                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label">{:lang('admin/system/configplay/showlist')}：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="play[showlist]" value="0" title="{:lang('close')}" {if condition="$play.showlist neq 1"}checked {/if}>
                        <input type="radio" name="play[showlist]" value="1" title="{:lang('open')}" {if condition="$play.showlist eq 1"}checked {/if}>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('admin/system/configplay/flag')}：</label>
                    <div class="layui-input-block">
                        <input type="radio" name="play[flag]" value="0" title="{:lang('admin/system/configplay/flag_tip')}" checked >
                    </div>
                </div>

                <div class="layui-form-item" style="display:none;">
                    <label class="layui-form-label">{:lang('admin/system/configplay/colors')}：</label>
                    <div class="layui-input-block">
                        <input type="text" id="mac_colors" name="play[colors]" placeholder="" value="{$play.colors}" class="layui-input">
                    </div>
                </div>
                    <div class="layui-form-item" style="display:none;">
                        <label class="layui-form-label">{:lang('admin/system/configplay/select_colors')}：</label>
                        <div class="layui-input-block">
                            <button class="layui-btn btn-primary radius" type="button" onclick="setColor(1);return false;" style="background-color: #000000"> 全黑色</button>
                            <button class="layui-btn btn-primary radius" type="button" onclick="setColor(2);return false;" style="background-color: #EFF4F7"> 浅蓝色</button>
                            <button class="layui-btn btn-primary radius" type="button" onclick="setColor(3);return false;" style="background-color: #D8CFDF"> 浅紫色</button>
                            <button class="layui-btn btn-primary radius" type="button" onclick="setColor(4);return false;" style="background-color: #D7E7B6"> 浅绿色</button>
                        </div>
                    </div>

                    <blockquote class="layui-elem-quote layui-quote-nm" style="display:none;">
                        {:lang('admin/system/configplay/select_colors_tip')}
                    </blockquote>

            </div>
            </div>

        </div>
        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit">{:lang('btn_save')}</button>
                <button type="button" class="layui-btn layui-btn-normal" id="btnDef">{:lang('default_val')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript">
    function setColor(v) {
        switch (v) {
            case 2:
                v = "EFF4F7,000000,666666,E4E4E4,000000,FF0000,FF0000,DBEBFE,458CE4,DBEBFE,FFFFFF,458CE4,DBEBFE,DBEBFE,fcfcfc";
                break;
            case 3:
                v = "D8CFDF,000000,666666,E4E4E4,000000,FF0000,FF0000,D8CFDF,926C92,BEAFC9,FFFFFF,926C92,BEAFC9,BEAFC9,fcfcfc";
                break;
            case 4:
                v = "D7E7B6,000000,666666,E4E4E4,000000,FF0000,FF0000,9EC14C,A3C656,BAD480,FFFFFF,A3C656,BAD480,BAD480,fcfcfc";
                break;
            default:
                v = "000000,F6F6F6,F6F6F6,333333,666666,FFFFF,FF0000,2c2c2c,ffffff,a3a3a3,2c2c2c,adadad,adadad,48486c,fcfcfc";
                break;
        }
        $("#mac_colors").val(v);
    }

    layui.use(['form', 'layer'], function(){
        // 操作对象
        var form = layui.form
            , layer = layui.layer;


        $('#btnDef').click(function(){
            $('input[name="play[width]"]').val('100%');
            $('input[name="play[height]"]').val('100%');
            $('input[name="play[widthmob]"]').val('100%');
            $('input[name="play[heightmob]"]').val('100%');
            $('input[name="play[widthpop]"]').val('600');
            $('input[name="play[heightpop]"]').val('500');
            $('input[name="play[second]"]').val('5');
            $('input[name="play[prestrain]"]').val('//union.maccms.pro/html/prestrain.html');
            $('input[name="play[buffer]"]').val('//union.maccms.pro/html/buffer.html');
            $('input[name="play[parse]"]').val('');

        });

    });


</script>

</body>
</html>