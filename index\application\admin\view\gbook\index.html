{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box" >
        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('data')}">
                <div class="layui-input-inline w100">
                    <select name="status">
                        <option value="">{:lang('select_status')}</option>
                        <option value="0" {if condition="$param['status'] == '0'"}selected {/if}>{:lang('reviewed_not')}</option>
                        <option value="1" {if condition="$param['status'] == '1'"}selected {/if}>{:lang('reviewed')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w100">
                    <select name="type">
                        <option value="">{:lang('select_reply_status')}</option>
                        <option value="1" {if condition="$param['reply'] eq '1'"}selected {/if}>{:lang('reply_not')}</option>
                        <option value="2" {if condition="$param['reply'] eq '2'"}selected {/if}>{:lang('reply_yes')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w100">
                    <select name="type">
                        <option value="">{:lang('select_genre')}</option>
                        <option value="1" {if condition="$param['type'] eq '1'"}selected {/if}>{:lang('gbook')}</option>
                        <option value="2" {if condition="$param['type'] eq '2'"}selected {/if}>{:lang('report')}</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>
        <div class="layui-btn-group">
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('index/select')}?tab=gbook&col=gbook_status&tpl=select_status&url=gbook/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('status')}</a>
            <a data-href="{:url('del')}?all=1" class="layui-btn layui-btn-primary j-ajax" confirm="{:lang('clear_confirm')}"><i class="layui-icon">&#xe640;</i>{:lang('clear')}</a>
        </div>
    </div>


        <form class="layui-form" method="post" id="pageListForm" >
            <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="60">{:lang('id')}</th>
                <th width="60">{:lang('status')}</th>
                <th width="60">{:lang('genre')}</th>
                <th >{:lang('gbook')}</th>
                <th >{:lang('report')}</th>
                <th width="100">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.gbook_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.gbook_id}</td>
                <td>{if condition="$vo.gbook_status eq 0"}<span class="layui-badge">{:lang('reviewed_not')}</span>{else}<span class="layui-badge layui-bg-green">{:lang('reviewed')}</span>{/if}</td>
                <td>{if condition="$vo.gbook_rid eq 0"}{:lang('gbook')}{else/}{:lang('report')}{/if}</td>
                <td>
                    <div class="c-999 f-12">
                        <u style="cursor:pointer" class="text-primary">{$vo.gbook_name|htmlspecialchars}：</u>
                        <time>【{$vo.gbook_time|mac_day=color}】</time>
                        <span class="ml-20">ip：【{$vo.gbook_ip|long2ip}】</span>
                    </div>
                    <div class="f-12 c-999">
                        <span class="ml-20">{:lang('status')}：</span>
                        {:lang('gbook')}：{$vo.gbook_content|htmlspecialchars}
                    </div>
                </td>
                <td>
                    <div class="c-999 f-12">
                        {:lang('reply_time')}：{$vo.gbook_reply_time|mac_day=color}
                    </div>
                    <div class="f-12 c-999">
                        {:lang('reply')}：{$vo.gbook_reply|htmlspecialchars}
                    </div>
                    <div> </div>
                </td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-href="{:url('info?id='.$vo['gbook_id'])}" href="javascript:;" title="{:lang('reply')}">{:lang('reply')}</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['gbook_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

        <div id="pages" class="center"></div>

    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    var curUrl="{:url('gbook/data',$param)}";
    layui.use(['laypage', 'layer','form'], function() {
        var laypage = layui.laypage
                , layer = layui.layer,
                form = layui.form;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });


    });
</script>
</body>
</html>