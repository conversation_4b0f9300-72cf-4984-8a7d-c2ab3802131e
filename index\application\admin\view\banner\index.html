{include file="../../../application/admin/view/public/head" /}
<style>
.layui-table img{width:100%;max-width:300px;}
</style>
<div class="page-container p10">

    <div class="my-toolbar-box" >

        <div class="center mb10">
            <form class="layui-form " method="post">
                <div class="layui-input-inline w150">
                    <select name="cat">
                        <option value="">选择广告位置</option>
						{volist name="cat" id="vo"}
						<option value="{$vo.cat_id}" {if condition="$param['cat'] == $vo['cat_id']"}selected {/if}>{$vo.cat_title}[{$vo.cat_code}]</option>
						{/volist}
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="status">
                        <option value="">选择状态</option>
                        <option value="0" {if condition="$param['status'] == '0'"}selected {/if}>关闭</option>
                        <option value="1" {if condition="$param['status'] == '1'"}selected {/if}>显示</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="请输入搜索条件" class="layui-input" name="wd" value="{$param['wd']}">
                </div>
                <button class="layui-btn mgl-20 j-search" >查询</button>
            </form>
        </div>
        <div class="layui-btn-group">
            <a data-full="1" data-href="{:url('infocat')}" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>添加位置</a>
            <a data-full="2" data-href="{:url('info')}" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>添加</a>
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>删除</a>
        </div>
    </div>

    <form class="layui-form" method="post" id="pageListForm" >
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="300">图片</th>
                <th >名称</th>
                <th width="160">链接</th>
                <th width="100">显示时间</th>
                <th width="50">显示状态</th>
                <th width="100">操作</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.banner_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td><img src="/{$vo.banner_pic}"></td>
                <td>{$vo.banner_title}</td>
                <td>{$vo.banner_link}</td>
                <td>{$vo.banner_stime|date='Y-m-d',###} - {$vo.banner_etime|date='Y-m-d',###}</td>
                <td>{if condition="$vo.banner_status eq 1"}<span class="layui-badge layui-bg-green ">显示</span>{else/}<span class="layui-badge">关闭</span>{/if}</td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('info?id='.$vo['banner_id'])}" href="javascript:;" title="编辑">编辑</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['banner_id'])}" href="javascript:;" title="删除">删除</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
        <div id="pages" class="center"></div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    var curUrl="{:url('banner/index',$param)}";
    layui.use(['laypage', 'layer','form'], function() {
        var laypage = layui.laypage
                , layer = layui.layer,
                form = layui.form;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });


    });
</script>
</body>
</html>