{include file="../../../application/admin/view/public/head" /}
<script type="text/javascript" src="__STATIC__/js/jquery.jscolor.js"></script>
{include file="../../../application/admin/view/public/editor" flag="actor_editor"/}
<div class="page-container p10">
    <div class="showpic" style="display:none;"><img class="showpic_img" width="120" height="160" referrerPolicy="no-referrer"></div>
    
    <form class="layui-form layui-form-pane" method="post" action="">
        <input type="hidden" name="actor_id" value="{$info.actor_id}">

        <div class="layui-tab">
            <ul class="layui-tab-title ">
                <li class="layui-this">{:lang('base_info')}</a></li>
                <li>{:lang('other_info')}</li>
            </ul>
            <div class="layui-tab-content">

                <div class="layui-tab-item layui-show">
                    
                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('param')}：</label>
                    <div class="layui-input-inline w150">
                        <select name="type_id" lay-filter="type_id">
                            <option value="">{:lang('select_type')}</option>
                            {volist name="type_tree" id="vo"}
                            {if condition="$vo.type_mid eq 8"}
                            <option value="{$vo.type_id}" {if condition="$info.type_id eq $vo.type_id"}selected{/if}>{$vo.type_name}</option>
                            {volist name="$vo.child" id="ch"}
                            <option value="{$ch.type_id}" {if condition="$info.type_id eq $ch.type_id"}selected{/if}>&nbsp;|&nbsp;&nbsp;&nbsp;|—{$ch.type_name}</option>
                            {/volist}
                            {/if}
                            {/volist}
                        </select>
                    </div>
                    <div class="layui-input-inline w150">
                            <select name="actor_level">
                                <option value="0">{:lang('select_level')}</option>
                                <option value="9" {if condition="$info.actor_level eq 9"}selected{/if}>{:lang('level')}9-{:lang('slide')}</option>
                                <option value="1" {if condition="$info.actor_level eq 1"}selected{/if}>{:lang('level')}1</option>
                                <option value="2" {if condition="$info.actor_level eq 2"}selected{/if}>{:lang('level')}2</option>
                                <option value="3" {if condition="$info.actor_level eq 3"}selected{/if}>{:lang('level')}3</option>
                                <option value="4" {if condition="$info.actor_level eq 4"}selected{/if}>{:lang('level')}4</option>
                                <option value="5" {if condition="$info.actor_level eq 5"}selected{/if}>{:lang('level')}5</option>
                                <option value="6" {if condition="$info.actor_level eq 6"}selected{/if}>{:lang('level')}6</option>
                                <option value="7" {if condition="$info.actor_level eq 7"}selected{/if}>{:lang('level')}7</option>
                                <option value="8" {if condition="$info.actor_level eq 8"}selected{/if}>{:lang('level')}8</option>
                            </select>
                    </div>
                    <div class="layui-input-inline w150">
                            <select name="actor_status">
                                <option value="1">{:lang('reviewed')}</option>
                                <option value="0" {if condition="$info.actor_status eq '0'"}selected{/if}>{:lang('reviewed_not')}</option>
                            </select>
                    </div>
                    <div class="layui-input-inline w150">
                        <select name="actor_sex">
                            <option value="{:lang('male')}">{:lang('male')}</option>
                            <option value="{:lang('female')}" {if condition="$info.actor_sex eq '女'"}selected{/if}>{:lang('female')}</option>
                        </select>
                    </div>
                    <div class="layui-input-inline w150">
                        <select name="actor_lock">
                            <option value="0">{:lang('unlock')}</option>
                            <option value="1" {if condition="$info.actor_lock eq 1"}selected{/if}>{:lang('lock')}</option>
                        </select>
                    </div>

                    <div class="layui-input-inline">
                        <input type="checkbox" name="uptime" title="{:lang('update_time')}" value="1" checked class="layui-checkbox checkbox-ids" lay-skin="primary">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('actor_name')}：</label>
                    <div class="layui-input-inline w200">
                        <input type="text" class="layui-input" value="{$info.actor_name}" placeholder="" name="actor_name">
                    </div>
                    <label class="layui-form-label">{:lang('alias')}：</label>
                    <div class="layui-input-inline w200">
                        <input type="text" class="layui-input" value="{$info.actor_alias}" placeholder="" name="actor_alias">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('en')}：</label>
                    <div class="layui-input-inline w200">
                        <input type="text" class="layui-input" value="{$info.actor_en}" placeholder="" name="actor_en">
                    </div>
                    <label class="layui-form-label">{:lang('letter')}：</label>
                    <div class="layui-input-inline w200">
                        <input type="text" class="layui-input" value="{$info.actor_letter}" placeholder="" name="actor_letter">
                    </div>
                    <label class="layui-form-label">{:lang('color')}：</label>
                    <div class="layui-input-inline w200">
                        <input type="text" class="layui-input color" value="{$info.actor_color}" placeholder="" name="actor_color">
                    </div>
                </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('blood')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_blood}" placeholder="A,B,AB,O" name="actor_blood">
                        </div>
                        <label class="layui-form-label">{:lang('area')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_area}" placeholder="" name="actor_area">
                        </div>
                        <label class="layui-form-label">{:lang('birtharea')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_birtharea}" placeholder="" name="actor_birtharea">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('height')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_height}" placeholder="cm" name="actor_height">
                        </div>
                        <label class="layui-form-label">{:lang('weight')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_weight}" placeholder="kg" name="actor_weight">
                        </div>
                        <label class="layui-form-label">{:lang('birthday')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_birthday}" placeholder="2000-01-01" name="actor_birthday">
                        </div>
                    </div>

                    <div class="layui-form-item">

                        <label class="layui-form-label">{:lang('starsign')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_starsign}" placeholder="" name="actor_starsign">
                        </div>
                        <label class="layui-form-label">{:lang('school')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_school}" placeholder="" name="actor_school">
                        </div>
                        <label class="layui-form-label">{:lang('remarks')}：</label>
                        <div class="layui-input-inline w200">
                            <input type="text" class="layui-input" value="{$info.actor_remarks}" placeholder="" name="actor_remarks" id="actor_remarks">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('works')}：</label>
                        <div class="layui-input-inline w800">
                            <input type="text" class="layui-input" value="{$info.actor_works}" placeholder="{:lang('multi_separate_tip')}" name="actor_works">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">TAG：</label>
                        <div class="layui-input-inline w500">
                            <input type="text" class="layui-input" value="{$info.actor_tag}" placeholder="" name="actor_tag">
                        </div>
                        <div class="layui-input-inline w120">
                            <input type="checkbox" name="uptag" title="{:lang('auto_make')}" value="1" class="layui-checkbox checkbox-ids" lay-skin="primary">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('class')}：</label>
                        <div class="layui-input-inline w500">
                            <input type="text" class="layui-input" value="{$info.actor_class}" placeholder="" id="actor_class" name="actor_class">
                        </div>
                        <div class="layui-input-inline w500 actor_class_label">

                        </div>
                    </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('pic')}：</label>
                    <div class="layui-input-inline w400 upload">
                        <input type="text" class="layui-input upload-input" style="max-width:100%;" value="{$info.actor_pic}" placeholder="" id="actor_pic" name="actor_pic">
                    </div>
                    <div class="layui-input-inline ">
                        <button type="button" class="layui-btn layui-upload" lay-data="" id="upload1">{:lang('upload_pic')}</button>
                    </div>
                </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('blurb')}：</label>
                        <div class="layui-input-block">
                            <textarea name="actor_blurb" cols="" rows="3" class="layui-textarea"  placeholder="{:lang('blurb_auto_tip')}" style="height:40px;">{$info.actor_blurb}</textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('content')}：</label>
                        <div class="layui-input-block">
                            <textarea id="actor_content" name="actor_content" type="text/plain" style="width:99%;height:300px">{$info.actor_content|mac_url_content_img}</textarea>
                        </div>
                    </div>
                    
        </div>

                <div class="layui-tab-item">
                        <div class="layui-form-item">
                            <label class="layui-form-label">{:lang('up')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_up}" placeholder="" id="actor_up" name="actor_up">
                            </div>
                            <label class="layui-form-label">{:lang('hate')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_down}" placeholder="" id="actor_down" name="actor_down">
                            </div>
                            <button class="layui-btn" type="button" id="btn_rnd">{:lang('rnd_make')}</button>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">{:lang('hits')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_hits}" placeholder="" id="actor_hits" name="actor_hits">
                            </div>
                            <label class="layui-form-label">{:lang('hits_month')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_hits_month}" placeholder="" id="actor_hits_month" name="actor_hits_month" >
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">{:lang('hits_week')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_hits_week}" placeholder="" id="actor_hits_week" name="actor_hits_week">
                            </div>
                            <label class="layui-form-label">{:lang('hits_day')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input " value="{$info.actor_hits_day}" placeholder="" id="actor_hits_day" name="actor_hits_day">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">{:lang('score')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_score}" placeholder="" id="actor_score" name="actor_score">
                            </div>
                            <label class="layui-form-label">{:lang('score_all')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_score_all}" placeholder="" id="actor_score_all" name="actor_score_all">
                            </div>
                            <label class="layui-form-label">{:lang('score_num')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_score_num}" placeholder="" id="actor_score_num" name="actor_score_num">
                            </div>
                        </div>

                        <div class="layui-form-item">
                            <label class="layui-form-label">{:lang('tpl')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_tpl}" placeholder="" name="actor_tpl">
                            </div>
                            <label class="layui-form-label">{:lang('jumpurl')}：</label>
                            <div class="layui-input-inline ">
                                <input type="text" class="layui-input" value="{$info.actor_jumpurl}" placeholder="" name="actor_jumpurl">
                            </div>
                        </div>
                    </div>
            </div>
        </div>

                <div class="layui-form-item center">
                    <div class="layui-input-block">
                        <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="">{:lang('btn_save')}</button>
                        <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
                    </div>
                </div>
    </form>

</div>
{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">

    layui.use(['form','upload', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery
                , upload = layui.upload;;

        // 验证
        form.verify({
            actor_name: function (value) {
                if (value == "") {
                    return "{:lang('name_empty')}";
                }
            }
        });

        $(document).on("click", ".extend", function(){
            $id = $(this).attr('data-id');
            if($id == 'actor_class'||$id == 'actor_keywords'){
                $val = $("input[id='"+$id+"']").val();
                if($val!=''){
                    $val = $val+',';
                }
                if($val.indexOf($(this).text())>-1){
                    return;
                }
                $("input[id='"+$id+"']").val($val+$(this).text());
            }else{
                $("input[id='"+$id+"']").val($(this).text());
            }
        });

        form.on('select(type_id)', function(data){
            getExtend(data.value);
        });

        upload.render({
            elem: '.layui-upload'
            ,url: "{:url('upload/upload')}?flag=actor"
            ,method: 'post'
            ,before: function(input) {
                layer.msg("{:lang('upload_ing')}", {time:3000000});
            },done: function(res, index, upload) {
                var obj = this.item;
                if (res.code == 0) {
                    layer.msg(res.msg);
                    return false;
                }
                layer.closeAll();
                var input = $(obj).parent().parent().find('.upload-input');
                if ($(obj).attr('lay-type') == 'image') {
                    input.siblings('img').attr('src', res.data.file).show();
                }
                input.val(res.data.file);

                if(res.data.thumb_class !=''){
                    $('.'+ res.data.thumb_class).val(res.data.thumb[0].file);
                }
            }
        });

        $('.upload-input').hover(function (e){
            var e = window.event || e;
            var imgsrc = $(this).val();
            if(imgsrc.trim()==""){ return; }
            var left = e.clientX+document.body.scrollLeft+20;
            var top = e.clientY+document.body.scrollTop+20;
            $(".showpic").css({left:left,top:top,display:""});
            if(imgsrc.indexOf('://')<0){ imgsrc = ROOT_PATH + '/' + imgsrc;	} else{ imgsrc = imgsrc.replace('mac:','http:'); }
            $(".showpic_img").attr("src", imgsrc);
        },function (e){
            $(".showpic").css("display","none");
        });

        $("#btn_rnd").click(function(){
            $("#actor_hits").val( rndNum(5000,9999) );
            $("#actor_hits_month").val( rndNum(1000,4999) );
            $("#actor_hits_week").val( rndNum(300,999) );
            $("#actor_hits_day").val( rndNum(1,299) );
            $("#actor_up").val( rndNum(1,999) );
            $("#actor_down").val( rndNum(1,999) );
            $("#actor_score").val( rndNum(10) );
            $("#actor_score_all").val( rndNum(1000) );
            $("#actor_score_num").val( rndNum(100) );
        });

        var ue = editor_getEditor('actor_content');
    });

    function getExtend(id){
        $.post("{:url('type/extend')}", {id:id}, function(res) {

            if (res.code == 1) {
                $.each(res.data, function(key, value){
                    $('.actor_'+key+"_label").html('');
                    if(value != ''){
                        $.each(value, function(key2, value2){
                            $(".actor_"+key+"_label").append('<a class="layui-btn layui-btn-xs extend" href="javascript:;" data-id="actor_'+key+'">'+value2+'</a>');
                        });
                    }
                });
            }
        });
    }

    {if condition="$info.actor_id gt 0"}
    setTimeout(function () {
        getExtend('{$info.type_id}')
    },1000);
    {/if}

</script>

</body>
</html>