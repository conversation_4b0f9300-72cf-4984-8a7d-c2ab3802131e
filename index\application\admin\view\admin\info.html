{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <form class="layui-form layui-form-pane" method="post" action="">
        <input id="admin_id" name="admin_id" type="hidden" value="{$info.admin_id}">
        <input type="hidden" name="__token__" value="{$Request.token}" />
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('id')}：</label>
            <div class="layui-input-block  ">
                <input type="text" class="layui-input" value="{$info.admin_name}" placeholder="" id="admin_name" name="admin_name">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('pass')}：</label>
            <div class="layui-input-block">
                <input type="password" class="layui-input" value="{$info.admin_pwd}" placeholder="" id="admin_pwd" name="admin_pwd">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('status')}：</label>
            <div class="layui-input-block">
                    <input name="admin_status" type="radio" id="rad-1" value="0" title="{:lang('disable')}" {if condition="$info['admin_status'] neq 1"}checked {/if}>
                    <input name="admin_status" type="radio" id="rad-2" value="1" title="{:lang('enable')}" {if condition="$info['admin_status'] eq 1"}checked {/if}>
            </div>
        </div>

        <div class="layui-form-item ">
            <label class="layui-form-label">{:lang('popedom')}：</label>
            <div class="layui-input-block">
                <blockquote class="layui-elem-quote layui-quote-nm">
                    {:lang('admin/admin/popedom_tip')}
                </blockquote>


                <div class="role-list-form ">
                    {volist name="menus" id="vo" key="k1"}
                    <dl class="role-list-form-top permission-list">
                        <dt>
                            <input type="checkbox" value="" {$vo.ck} lay-skin="primary" data-id="{$k1}" lay-filter="roleAuth1" title="{$vo.name}">
                        </dt>
                        <dd>
                            {volist name="$vo.sub" id="sub" key="k2"}
                                <input type="checkbox" value="{$sub.controller}/{$sub.action}" name="admin_auth[]" {$sub.ck} data-pid="{$k1}" title="{$sub.name}" lay-skin="primary" lay-filter="roleAuth2">
                            {/volist}
                        </dd>
                    </dl>
                    {/volist}
                </div>

            </div>
        </div>

        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-normal formCheckAll" lay-filter="formCheckAll" >{:lang('check_all')}</button>
                <button type="button" class="layui-btn layui-btn-normal formCheckOther" lay-filter="formCheckOther">{:lang('check_other')}</button>

                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="true">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>

</div>
{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    layui.use(['form', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery;

        // 验证
        form.verify({
            admin_name: function (value) {
                if (value == "") {
                    return "{:lang('name_empty')}";
                }
            },
            admin_pwd: function (value) {
                if (value == "") {
                    return "{:lang('pass_empty')}";
                }
            }
        });

        form.on('checkbox(roleAuth1)', function(data) {
            var child = $(data.elem).parent('dt').siblings('dd').find('input');
            /* 自动选中子节点 */
            child.each(function(index, item) {
                if(item.disabled == true){

                }
                else {
                    item.checked = data.elem.checked;
                }
            });
            form.render('checkbox');
        });

        form.on('checkbox(roleAuth2)', function(data) {
            var child = $(data.elem).parent().find('input');
            var parent = $(data.elem).parent('dd').siblings('dt').find('input');
            var parent_ck= true;
            /* 自动选中子节点 */
            child.each(function(index, item) {
                if(!item.checked){
                    parent_ck = false;
                }
            });
            parent.each(function(index, item) {
                item.checked = parent_ck;
            });
            form.render('checkbox');
        });


        $('.formCheckAll').click(function(){
            var child = $('.role-list-form-top').find('input');
            /* 自动选中子节点 */
            child.each(function(index, item) {
                item.checked = true;
            });
            form.render('checkbox');
        });
        $('.formCheckOther').click(function(){
            var child = $('.role-list-form-top').find('input');
            /* 自动选中子节点 */
            child.each(function(index, item) {
                item.checked = (item.checked  ? false : true);
            });
            form.render('checkbox');
        });

    });

</script>
</body>
</html>