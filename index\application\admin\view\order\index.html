{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box" >
        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('index')}">
                <div class="layui-input-inline w150">
                    <select name="status">
                        <option value="">{:lang('select_order_status')}</option>
                        <option value="0" {if condition="$param['status'] eq '0'"}selected {/if}>{:lang('not_paid')}</option>
                        <option value="1" {if condition="$param['status'] eq '1'"}selected {/if}>{:lang('paid')}</option>
                    </select>
                </div>
                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('del')}?ids=1&all=1" class="layui-btn layui-btn-primary j-ajax" confirm="{:lang('clear_confirm')}"><i class="layui-icon">&#xe640;</i>{:lang('clear')}</a>
        </div>

    </div>

     <form class="layui-form " method="post" id="pageListForm">
         <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="50">{:lang('id')}</th>
                <th width="100">{:lang('admin/order/order_no')}</th>
                <th width="80">{:lang('admin/order/order_money')}</th>
                <th width="80">{:lang('admin/order/order_status')}</th>
                <th width="130">{:lang('admin/order/order_time')}</th>
                <th width="100">{:lang('admin/order/pay_type')}</th>
                <th width="130">{:lang('admin/order/pay_time')}</th>
                <th width="80">{:lang('user')}</th>
                <th width="50">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.order_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.order_id}</td>
                <td>{$vo.order_code}</td>
                <td>{$vo.order_price}</td>
                <td>{$vo.order_status|mac_get_order_status_text}</td>
                <td>{$vo.order_time|mac_day=color}</td>
                <td>{$vo.order_type}</td>
                <td>{$vo.order_paytime|mac_day=color}</td>
                <td>{$vo.user_id}、{$vo.user_name}</td>
                <td>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['order_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>

        <div id="pages" class="center"></div>

    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}


<script type="text/javascript">
    var curUrl="{:url('order/index',$param)}";
    layui.use(['laypage', 'layer'], function() {
        var laypage = layui.laypage
                , layer = layui.layer;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });
    });
</script>
</body>
</html>