{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <form class="layui-form layui-form-pane" method="post" action="">
        <input id="link_id" name="link_id" type="hidden" value="{$info.link_id}">
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('admin/card/make_num')}：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="10" lay-verify="num" placeholder="" name="num">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('money')}：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="" lay-verify="money" placeholder="" name="money">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('points')}：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="" lay-verify="point" placeholder="" name="point">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('card_no')}{:lang('rule')}：</label>
            <div class="layui-input-block">
                <input type="radio" name="role_no" value="" title="{:lang('mixing')}" checked>
                <input type="radio" name="role_no" value="letter" title="{:lang('abc')}" >
                <input type="radio" name="role_no" value="num" title="{:lang('number')}" >
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('pass')}{:lang('rule')}：</label>
            <div class="layui-input-block">
                <input type="radio" name="role_pwd" value="" title="{:lang('mixing')}" checked>
                <input type="radio" name="role_pwd" value="letter" title="{:lang('abc')}" >
                <input type="radio" name="role_pwd" value="num" title="{:lang('number')}" >
            </div>
        </div>

        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="true">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>

</div>
{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    layui.use(['form', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery;

        // 验证
        form.verify({
            num: function (value) {
                if (value == "") {
                    return "{:lang('admin/card/please_input_make_num')}";
                }
            },
            money: function (value) {
                if (value == "") {
                    return "{:lang('admin/card/please_input_money')}";
                }
            },
            point: function (value) {
                if (value == "") {
                    return "{:lang('admin/card/please_input_points')}";
                }
            }
        });


    });
</script>

</body>
</html>