{include file="../../../application/admin/view/public/head" /}

<div class="page-container">

    <div class="showpic" style="display:none;"><img class="showpic_img" width="120" height="160" referrerPolicy="no-referrer"></div>

    <form class="layui-form layui-form-pane" action="">
        <input type="hidden" name="__token__" value="{$Request.token}" />
        <div class="layui-tab">
            <ul class="layui-tab-title">
                <li class="layui-this">{:lang('admin/system/configupload/title')}</li>
            </ul>
            <div class="layui-tab-content">

                <div class="layui-tab-item layui-show">

                    <blockquote class="layui-elem-quote layui-quote-nm">
                        {:lang('admin/system/configupload/tip')}{:sys_get_temp_dir()}<br>
                        <?php
                        $temp_file = tempnam(sys_get_temp_dir(), 'Tux');
                        if($temp_file){
                            echo '<span class="layui-badge layui-bg-green">'.lang('admin/system/configupload/write_ok').'</span>';
                        }
                        else{
                            echo '<span class="layui-badge">'.lang('admin/system/configupload/write_err').'</span>';
                        }
                      ?>
                    </blockquote>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/img_key')}：</label>
                        <div class="layui-input-inline w500">
                            <input type="text" name="upload[img_key]" placeholder="" value="{$config['upload']['img_key']}" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configupload/img_key_tip')}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/img_api')}：</label>
                        <div class="layui-input-inline w500">
                            <input type="text" name="upload[img_api]" placeholder="" value="{$config['upload']['img_api']}" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configupload/img_api_tip')}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('pic_thumb')}：</label>
                        <div class="layui-input-inline">
                            <input type="radio" name="upload[thumb]" value="0" title="{:lang('close')}" {if condition="$config['upload']['thumb'] neq 1"}checked {/if}>
                            <input type="radio" name="upload[thumb]" value="1" title="{:lang('open')}" {if condition="$config['upload']['thumb'] eq 1"}checked {/if}>
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configupload/thumb_tip')}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/thumb_size')}：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="upload[thumb_size]" placeholder="" value="{$config['upload']['thumb_size']}" class="layui-input w150">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configupload/thumb_size_tip')}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/thumb_type')}：</label>
                        <div class="layui-input-inline">
                            <select class="w150" name="upload[thumb_type]">
                                <option value="1" {if condition="$config['upload']['thumb_type'] eq 1"}selected {/if}>{:lang('admin/system/configupload/thumb_type1')}</option>
                                <option value="2" {if condition="$config['upload']['thumb_type'] eq 2"}selected {/if}>{:lang('admin/system/configupload/thumb_type2')}</option>
                                <option value="3" {if condition="$config['upload']['thumb_type'] eq 3"}selected {/if}>{:lang('admin/system/configupload/thumb_type3')}</option>
                                <option value="4" {if condition="$config['upload']['thumb_type'] eq 4"}selected {/if}>{:lang('admin/system/configupload/thumb_type4')}</option>
                                <option value="5" {if condition="$config['upload']['thumb_type'] eq 5"}selected {/if}>{:lang('admin/system/configupload/thumb_type5')}</option>
                                <option value="6" {if condition="$config['upload']['thumb_type'] eq 6"}selected {/if}>{:lang('admin/system/configupload/thumb_type6')}</option>
                            </select>
                        </div>
                        <div class="layui-form-mid layui-word-aux"></div>
                    </div>
                <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/watermark')}：</label>
                        <div class="layui-input-inline">
                            <input type="radio" name="upload[watermark]" value="0" title="{:lang('close')}" {if condition="$config['upload']['watermark'] neq 1"}checked {/if}>
                            <input type="radio" name="upload[watermark]" value="1" title="{:lang('open')}" {if condition="$config['upload']['watermark'] eq 1"}checked {/if}>
                        </div>
                </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/watermark_location')}：</label>
                        <div class="layui-input-inline">
                            <select class="w150" name="upload[watermark_location]">

                                <option value="1" {if condition="$config['upload']['watermark_location'] eq 1"}selected {/if}>{:lang('admin/system/configupload/watermark_location1')}</option>
                                <option value="2" {if condition="$config['upload']['watermark_location'] eq 2"}selected {/if}>{:lang('admin/system/configupload/watermark_location2')}</option>
                                <option value="3" {if condition="$config['upload']['watermark_location'] eq 3"}selected {/if}>{:lang('admin/system/configupload/watermark_location3')}</option>
                                <option value="4" {if condition="$config['upload']['watermark_location'] eq 4"}selected {/if}>{:lang('admin/system/configupload/watermark_location4')}</option>
                                <option value="5" {if condition="$config['upload']['watermark_location'] eq 5"}selected {/if}>{:lang('admin/system/configupload/watermark_location5')}</option>
                                <option value="6" {if condition="$config['upload']['watermark_location'] eq 6"}selected {/if}>{:lang('admin/system/configupload/watermark_location6')}</option>
                                <option value="7" {if condition="$config['upload']['watermark_location'] eq 7"}selected {/if}>{:lang('admin/system/configupload/watermark_location7')}</option>
                                <option value="8" {if condition="$config['upload']['watermark_location'] eq 8"}selected {/if}>{:lang('admin/system/configupload/watermark_location8')}</option>
                                <option value="9" {if condition="$config['upload']['watermark_location'] eq 9"}selected {/if}>{:lang('admin/system/configupload/watermark_location9')}</option>


                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/watermark_content')}：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="upload[watermark_content]" placeholder="" value="{$config['upload']['watermark_content']}" class="layui-input w150"  >
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/watermark_size')}：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="upload[watermark_size]" placeholder="{:lang('admin/system/configupload/watermark_size_tip')}" value="{$config['upload']['watermark_size']}" class="layui-input w150"  >
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/watermark_color')}：</label>
                        <div class="layui-input-inline">
                            <input type="text" name="upload[watermark_color]" placeholder="{:lang('admin/system/configupload/watermark_color_tip')}" value="{$config['upload']['watermark_color']}" class="layui-input w150"  >
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configupload/protocol')}：</label>
                        <div class="layui-input-inline">
                            <select class="w150" name="upload[protocol]" lay-filter="upload[protocol]">
                                <option value="http" {if condition="$config['upload']['protocol'] eq 'http'"}selected {/if}>http</option>
                                <option value="https" {if condition="$config['upload']['protocol'] eq 'https'"}selected {/if}>https</option>
                            </select>
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configupload/protocol_tip')}</div>
                    </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">{:lang('admin/system/configupload/mode')}：</label>
                    <div class="layui-input-inline">
                        <select class="w150" name="upload[mode]" lay-filter="upload[mode]">
                            <option value="local" {if condition="$config['upload']['mode'] eq 'local'"}selected {/if}>{:lang('admin/system/configupload/mode_local')}</option>
                            <option value="remote" {if condition="$config['upload']['mode'] eq 'remote'"}selected {/if}>{:lang('admin/system/configupload/mode_remote')}</option>
                            {volist name="$extends['ext_list']" id="vo"}
                            <option value="{$key}" {if condition="$config['upload']['mode'] eq $key"}selected {/if}>{$vo}</option>
                            {/volist}
                        </select>
                    </div>
                </div>

                <div class="layui-form-item upload_mode mode_remote" {if condition="$config['upload']['mode'] neq 'remote'"}style="display:none;" {/if}>
                    <label class="layui-form-label">{:lang('admin/system/configupload/remoteurl')}：</label>
                    <div class="layui-input-block">
                        <input type="text" name="upload[remoteurl]" placeholder="{:lang('admin/system/configupload/remoteurl_tip')}" value="{$config['upload']['remoteurl']}" class="layui-input w500">
                    </div>
                </div>

                {$extends['ext_html']}

                </div>


                <div class="layui-form-item center">
                    <div class="layui-input-block">
                        <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit">{:lang('btn_save')}</button>
                        <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript">
    layui.use(['form','layer'], function(){
        // 操作对象
        var form = layui.form
                , layer = layui.layer;

        form.on('select(upload[mode])', function(data){
            $('.upload_mode').hide();
            $('.mode_'+ data.value).show();
        });


    });


</script>

</body>
</html>