{include file="../../../application/admin/view/public/head" /}

<div class="page-container p10">
    <form class="layui-form layui-form-pane" method="post" action="">
        <input id="link_id" name="link_id" type="hidden" value="{$info.link_id}">
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('name')}：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{$info.link_name}" lay-verify="link_name" placeholder="" id="link_name" name="link_name">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('url')}：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{$info.link_url}" lay-verify="link_url" placeholder="" id="link_url" name="link_url">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('logo')}：</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" value="{$info.link_logo}" placeholder="" id="link_logo" name="link_logo">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('sort')}：</label>
            <div class="layui-input-inline w100">
                <input type="text" class="layui-input" value="{$info.link_sort}" placeholder="" id="link_sort" name="link_sort">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('genre')}：</label>
            <div class="layui-input-inline">
                <select class="w100" name="link_type">
                    <option value="0" {if condition="$vo['link_type'] eq 0"}selected {/if}>{:lang('admin/link/text_link')}</option>
                    <option value="1" {if condition="$vo['link_type'] eq 1"}selected {/if}>{:lang('admin/link/pic_link')}</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="true">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>

</div>

{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    layui.use(['form', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery;

        // 验证
        form.verify({
            link_name: function (value) {
                if (value == "") {
                    return "{:lang('name_empty')}";
                }
            },
            link_url: function (value) {
                if (value == "") {
                    return "{:lang('url_empty')}";
                }
            }
        });


    });
</script>

</body>
</html>