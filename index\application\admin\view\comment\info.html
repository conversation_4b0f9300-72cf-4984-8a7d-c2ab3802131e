{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">
    <form class="layui-form layui-form-pane" method="post" action="">
        <input id="comment_id" name="comment_id" type="hidden" value="{$info.comment_id}">
        <input id="comment_mid" name="comment_mid" type="hidden" value="{$info.comment_mid}">
        <input id="comment_rid" name="comment_rid" type="hidden" value="{$info.comment_rid}">
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('model')}：</label>
            <div class="layui-input-inline w80">
                <input type="text" class="layui-input" value="{$info.comment_mid|mac_get_mid_text}" readonly="readonly">
            </div>
            <label class="layui-form-label">{:lang('nickname')}：</label>
            <div class="layui-input-inline w80">
                <input type="text" class="layui-input" value="{$info.comment_name}" readonly="readonly" name="comment_name" >
            </div>
            <label class="layui-form-label">{:lang('time')}：</label>
            <div class="layui-input-inline w130">
                <input type="text" class="layui-input" value="{$info.comment_time|date='Y-m-d H:i:s',###}" readonly="readonly">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('up')}：</label>
            <div class="layui-input-inline w80">
                <input type="text" class="layui-input" value="{$info.comment_up}" name="comment_up">
            </div>
            <label class="layui-form-label">{:lang('hate')}：</label>
            <div class="layui-input-inline w80">
                <input type="text" class="layui-input" value="{$info.comment_down}" name="comment_down">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">{:lang('content')}：</label>
            <div class="layui-input-block">
                <textarea type="text" class="layui-textarea" lay-verify="comment_content" placeholder="" name="comment_content">{$info.comment_content}</textarea>
            </div>
        </div>

        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit" data-child="true">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>

</div>
{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    layui.use(['form', 'layer'], function () {
        // 操作对象
        var form = layui.form
                , layer = layui.layer
                , $ = layui.jquery;

        // 验证
        form.verify({
            comment_content: function (value) {
                if (value == "") {
                    return "{:lang('content_empty')}";
                }
            }
        });


    });
</script>

</body>
</html>