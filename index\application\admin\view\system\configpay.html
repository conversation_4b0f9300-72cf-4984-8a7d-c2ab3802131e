{include file="../../../application/admin/view/public/head" /}

<div class="page-container">
    <form class="layui-form layui-form-pane" action="">
        <input type="hidden" name="__token__" value="{$Request.token}" />
        <div class="layui-tab" lay-filter="tb1">
            <ul class="layui-tab-title">
                <li class="layui-this" lay-id="configpay_1">{:lang('admin/system/configpay/title')}</li>
                <li lay-id="configpay_2">{:lang('admin/system/configpay/card')}</li>
                {volist name="$extends['ext_list']" id="vo"}
                <li data-key="{$key}" lay-id="configpay_{$i+2}">{$vo}</li>
                {/volist}
                
            </ul>
            <div class="layui-tab-content">

                <div class="layui-tab-item layui-show">

                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                        <legend>{:lang('admin/system/configpay/config')}</legend>
                    </fieldset>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configpay/notify')}：</label>
                        <div class="layui-input-inline w400">
                            <input type="text" readonly="readonly" value="{$http_type}{$config['site']['site_url']}/index.php/payment/notify.html" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configpay/notify_tip')}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configpay/min')}：</label>
                        <div class="layui-input-inline w400">
                            <input type="text" name="pay[min]" placeholder="" value="{$config['pay']['min']}" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configpay/min_tip')}</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configpay/scale')}：</label>
                        <div class="layui-input-inline w400">
                            <input type="text" name="pay[scale]" placeholder="" value="{$config['pay']['scale']}" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configpay/scale_tip')}</div>
                    </div>
                </div>

                <div class="layui-tab-item ">

                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                        <legend>{:lang('admin/system/configpay/card_config')}</legend>
                    </fieldset>

                    <div class="layui-form-item">
                        <label class="layui-form-label">{:lang('admin/system/configpay/card_url')}：</label>
                        <div class="layui-input-inline w400">
                            <input type="text" name="pay[card][url]" placeholder="" value="{$config['pay']['card']['url']}" class="layui-input">
                        </div>
                        <div class="layui-form-mid layui-word-aux">{:lang('admin/system/configpay/card_url_tip')}</div>
                    </div>
                </div>

                {$extends['ext_html']}

            </div>
        </div>
        <div class="layui-form-item center">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit="" lay-filter="formSubmit">{:lang('btn_save')}</button>
                <button class="layui-btn layui-btn-warm" type="reset">{:lang('btn_reset')}</button>
            </div>
        </div>
    </form>
</div>

{include file="../../../application/admin/view/public/foot" /}
<script type="text/javascript" src="__STATIC__/js/jquery.cookie.js"></script>
<script type="text/javascript">
    layui.use(['element', 'form', 'layer'], function() {
        var element = layui.element
            ,form = layui.form
            , layer = layui.layer;


        element.on('tab(tb1)', function(){
            $.cookie('configpay_tab', this.getAttribute('lay-id'));
        });

        if( $.cookie('configpay_tab') !=null ) {
            element.tabChange('tb1', $.cookie('configpay_tab'));
        }

    });
</script>

</body>
</html>