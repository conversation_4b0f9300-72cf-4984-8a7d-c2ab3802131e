{include file="../../../application/admin/view/public/head" /}
<div class="page-container p10">

    <div class="my-toolbar-box">

        <div class="center mb10">
            <form class="layui-form " method="post" action="{:url('data')}">
                <input type="hidden" value="{$param.select}" name="select">
                <input type="hidden" value="{$param.input}" name="input">
                <div class="layui-input-inline w150">
                    <select name="type">
                        <option value="">{:lang('select_type')}</option>
                        {volist name="type_tree" id="vo"}
                        {if condition="$vo.type_mid eq 2"}
                        <option value="{$vo.type_id}" {if condition="$param['type'] eq $vo.type_id"}selected {/if}>{$vo.type_name}</option>
                        {volist name="vo.child" id="ch"}
                        <option value="{$ch.type_id}" {if condition="$param['type'] eq $ch.type_id"}selected {/if}>&nbsp;&nbsp;&nbsp;&nbsp;├&nbsp;{$ch.type_name}</option>
                        {/volist}
                        {/if}
                        {/volist}
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="status">
                        <option value="">{:lang('select_status')}</option>
                        <option value="0" {if condition="$param['status'] eq '0'"}selected {/if}>{:lang('reviewed_not')}</option>
                        <option value="1" {if condition="$param['status'] eq '1'"}selected {/if}>{:lang('reviewed')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="level">
                        <option value="">{:lang('select_level')}</option>
                        <option value="9" {if condition="$param['level'] eq '9'"}selected {/if}>{:lang('level')}9-{:lang('slide')}</option>
                        <option value="1" {if condition="$param['level'] eq '1'"}selected {/if}>{:lang('level')}1</option>
                        <option value="2" {if condition="$param['level'] eq '2'"}selected {/if}>{:lang('level')}2</option>
                        <option value="3" {if condition="$param['level'] eq '3'"}selected {/if}>{:lang('level')}3</option>
                        <option value="4" {if condition="$param['level'] eq '4'"}selected {/if}>{:lang('level')}4</option>
                        <option value="5" {if condition="$param['level'] eq '5'"}selected {/if}>{:lang('level')}5</option>
                        <option value="6" {if condition="$param['level'] eq '6'"}selected {/if}>{:lang('level')}6</option>
                        <option value="7" {if condition="$param['level'] eq '7'"}selected {/if}>{:lang('level')}7</option>
                        <option value="8" {if condition="$param['level'] eq '8'"}selected {/if}>{:lang('level')}8</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="lock">
                        <option value="">{:lang('select_lock')}</option>
                        <option value="0" {if condition="$param['lock'] eq '0'"}selected {/if}>{:lang('unlock')}</option>
                        <option value="1" {if condition="$param['lock'] eq '1'"}selected {/if}>{:lang('lock')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="pic">
                        <option value="">{:lang('select_pic')}</option>
                        <option value="1" {if condition="$param['pic'] eq '1'"}selected{/if}>{:lang('pic_empty')}</option>
                        <option value="2" {if condition="$param['pic'] eq '2'"}selected{/if}>{:lang('pic_remote')}</option>
                        <option value="3" {if condition="$param['pic'] eq '3'"}selected{/if}>{:lang('pic_sync_err')}</option>
                    </select>
                </div>
                <div class="layui-input-inline w150">
                    <select name="order">
                        <option value="">{:lang('select_sort')}</option>
                        <option value="art_time" {if condition="$param['order'] eq 'art_time'"}selected{/if}>{:lang('update_time')}</option>
                        <option value="art_id" {if condition="$param['order'] eq 'art_id'"}selected{/if}>{:lang('id')}</option>
                        <option value="art_hits" {if condition="$param['order'] eq 'art_hits'"}selected{/if}>{:lang('hits')}</option>
                        <option value="art_hits_month" {if condition="$param['order'] eq 'art_hits_month'"}selected{/if}>{:lang('hits_month')}</option>
                        <option value="art_hits_week" {if condition="$param['order'] eq 'art_hits_week'"}selected{/if}>{:lang('hits_week')}</option>
                        <option value="art_hits_day" {if condition="$param['order'] eq 'art_hits_day'"}selected{/if}>{:lang('hits_day')}</option>
                    </select>
                </div>

                <div class="layui-input-inline">
                    <input type="text" autocomplete="off" placeholder="{:lang('wd')}" class="layui-input" name="wd" value="{$param['wd']}">
                </div>
                <button class="layui-btn mgl-20 j-search" >{:lang('btn_search')}</button>
            </form>
        </div>

        <div class="layui-btn-group">
            <a data-href="{:url('info')}" data-full="1" class="layui-btn layui-btn-primary j-iframe"><i class="layui-icon">&#xe654;</i>{:lang('add')}</a>
            <a data-href="{:url('del')}" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del')}</a>
            <a data-href="{:url('index/select')}?tab=art&col=type_id&tpl=select_type&url=art/field" data-width="270" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('type')}</a>
            <a data-href="{:url('index/select')}?tab=art&col=art_level&tpl=select_level&url=art/field" data-width="270" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('level')}</a>
            <a data-href="{:url('index/select')}?tab=art&col=art_hits&tpl=select_hits&url=art/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('hits')}</a>
            <a data-href="{:url('index/select')}?tab=art&col=art_status&tpl=select_status&url=art/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('status')}</a>
            <a data-href="{:url('index/select')}?tab=art&col=art_lock&tpl=select_lock&url=art/field" data-width="470" data-height="100" data-checkbox="1" class="layui-btn layui-btn-primary j-select"><i class="layui-icon">&#xe620;</i>{:lang('lock')}</a>
            <a class="layui-btn layui-btn-primary j-iframe" data-href="{:url('images/opt?tab=art')}" href="javascript:;" title="{:lang('pic_sync')}"><i class="layui-icon">&#xe620;</i>{:lang('pic_sync')}</a>
            <a class="layui-btn layui-btn-primary j-iframe" data-checkbox="true" data-href="{:url('make/make?ac=info&tab=art')}" href="javascript:;" title="{:lang('make_page')}"><i class="layui-icon">&#xe620;</i>{:lang('make_page')}</a>
            {if condition="$param.select eq 1"}
            <a data-href="" onclick="parent.onSelectResult('{$param.input}',$('.checkbox-ids:checked'))" class="layui-btn layui-btn-normal">{:lang('select_return')}</a>
            {/if}
            {if condition="$param['repeat'] neq ''"}
            <a data-href="{:url('del')}?repeat=1&retain=min" data-checkbox="no" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del_auto_keep_min')}</a>
            <a data-href="{:url('del')}?repeat=1&retain=max" data-checkbox="no" class="layui-btn layui-btn-primary j-page-btns confirm"><i class="layui-icon">&#xe640;</i>{:lang('del_auto_keep_max')}</a>
            {/if}
        </div>

    </div>


    <form class="layui-form " method="post" id="pageListForm">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th width="25"><input type="checkbox" lay-skin="primary" lay-filter="allChoose"></th>
                <th width="40">{:lang('id')}</th>
                <th >{:lang('name')}</th>
                <th width="50">{:lang('hits')}</th>
                <th width="30">{:lang('score')}</th>
                <th width="30">{:lang('level')}</th>
                <th width="30">{:lang('browse')}</th>
                <th width="80">{:lang('author')}</th>
                <th width="120">{:lang('update_time')}</th>
                <th width="80">{:lang('opt')}</th>
            </tr>
            </thead>

            {volist name="list" id="vo"}
            <tr>
                <td><input type="checkbox" name="ids[]" value="{$vo.art_id}" class="layui-checkbox checkbox-ids" lay-skin="primary"></td>
                <td>{$vo.art_id}</td>
                <td>[{$vo.type.type_name}] <a target="_blank" class="layui-badge-rim " href="{:mac_url_art_detail($vo)}">{$vo.art_name|htmlspecialchars}</a> {if condition="$vo.art_status eq 0"} <span class="layui-badge">{:lang('reviewed_not')}</span>{/if} {if condition="$vo.art_lock eq 1"} <span class="layui-badge">{:lang('lock')}</span>{/if}</td>
                <td>{$vo.art_hits}</td>
                <td>{$vo.art_score}</td>
                <td><a data-href="{:url('index/select')}?tab=art&col=art_level&tpl=select_level&url=art/field&ids={$vo.art_id}" data-width="270" data-height="100" class=" j-select"><span class="layui-badge layui-bg-orange">{$vo.art_level}</span></a></td>
                <td>{if condition="$vo.ismake eq 1"}<a target="_blank" class="layui-badge layui-bg-green " href="{:mac_url_art_detail($vo)}">Y</a>{else/}<a class="layui-badge" href="{:url('make/make?ac=info&tab=art')}?ids={$vo.art_id}&ref=1">N</a>{/if}</td>
                <td>{$vo.art_author|htmlspecialchars}</td>
                <td>{$vo.art_time|mac_day=color}</td>
                <td>
                    <a class="layui-badge-rim j-iframe" data-full="1" data-href="{:url('info?id='.$vo['art_id'])}" href="javascript:;" title="{:lang('edit')}">{:lang('edit')}</a>
                    <a class="layui-badge-rim j-tr-del" data-href="{:url('del?ids='.$vo['art_id'])}" href="javascript:;" title="{:lang('del')}">{:lang('del')}</a>
                </td>
            </tr>
            {/volist}
            </tbody>
        </table>
        <div id="pages" class="center"></div>
    </form>
</div>




{include file="../../../application/admin/view/public/foot" /}

<script type="text/javascript">
    var curUrl="{:url('art/data',$param)}";
    layui.use(['laypage', 'layer','form'], function() {
        var laypage = layui.laypage
                , layer = layui.layer,
                form = layui.form;

        laypage.render({
            elem: 'pages'
            ,count: {$total}
            ,limit: {$limit}
            ,curr: {$page}
            ,layout: ['count', 'prev', 'page', 'next', 'limit', 'skip']
            ,jump: function(obj,first){
                if(!first){
                    location.href = curUrl.replace('%7Bpage%7D',obj.curr).replace('%7Blimit%7D',obj.limit);
                }
            }
        });


    });
</script>
</body>
</html>