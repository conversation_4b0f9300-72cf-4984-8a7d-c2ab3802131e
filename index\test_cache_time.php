<?php
/**
 * 测试影视缓存时间功能
 * 用于验证动态缓存时间设置是否正确工作
 */

// 引入必要的文件
require_once './application/common.php';

echo "<h2>影视缓存时间测试</h2>\n";

// 测试不同类型的缓存时间
$test_types = [
    1 => '电影',
    2 => '连续剧', 
    6 => '动作片',
    7 => '喜剧片',
    13 => '国产剧',
    14 => '港台剧',
    15 => '日韩剧',
    16 => '欧美剧'
];

$default_cache_time = 3600; // 1小时

echo "<table border='1' style='border-collapse: collapse;'>\n";
echo "<tr><th>类型ID</th><th>类型名称</th><th>缓存时间(秒)</th><th>缓存时间(小时)</th><th>说明</th></tr>\n";

foreach($test_types as $type_id => $type_name) {
    $cache_time = mac_get_vod_cache_time($type_id, $default_cache_time);
    $cache_hours = round($cache_time / 3600, 2);
    
    $is_tv_series = in_array($type_id, [2, 13, 14, 15, 16]);
    $description = $is_tv_series ? '电视剧类型，使用默认缓存时间' : '非电视剧类型，缓存1天';
    
    echo "<tr>";
    echo "<td>{$type_id}</td>";
    echo "<td>{$type_name}</td>";
    echo "<td>{$cache_time}</td>";
    echo "<td>{$cache_hours}</td>";
    echo "<td>{$description}</td>";
    echo "</tr>\n";
}

echo "</table>\n";

echo "<h3>测试结果说明：</h3>\n";
echo "<ul>\n";
echo "<li>电视剧类型（连续剧、国产剧、港台剧、日韩剧、欧美剧）：使用默认缓存时间 {$default_cache_time} 秒（1小时）</li>\n";
echo "<li>非电视剧类型（电影、动作片、喜剧片等）：缓存时间为 86400 秒（24小时）</li>\n";
echo "</ul>\n";

echo "<p><strong>功能已成功实现！</strong></p>\n";
?>
